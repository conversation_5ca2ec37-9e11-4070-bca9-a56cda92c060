import json
import os
from unittest.mock import MagicMock, patch

from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.actions.event_sync import (
    convert_to_ocsf,
    map_nozomi_severity_to_ocsf,
    map_nozomi_status_to_ocsf,
    normalize_event,
)
from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.api import (
    NozomiVantageV1Api,
    NozomiVantageV1ApiError,
)
from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.health_check import (
    ConnectionHealthCheck,
)
from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.integration import (
    NozomiVantageV1Integration,
)
from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.tests.base import BaseTestCase


class NozomiVantageV1ApiTest(BaseTestCase):
    """Test cases for NozomiVantageV1Api."""

    def test_api_init(self):
        """Test NozomiVantageV1Api initialization."""
        api = NozomiVantageV1Api(
            url="https://example.com",
            api_key_name="test_key",
            api_key_token="test_token"
        )
        self.assertIsInstance(api, NozomiVantageV1Api)
        self.assertEqual(api.url, "https://example.com")
        self.assertEqual(api.api_key_name, "test_key")
        self.assertEqual(api.api_key_token, "test_token")

    def test_api_init_missing_params(self):
        """Test NozomiVantageV1Api initialization with missing parameters."""
        with self.assertRaises(ValueError):
            NozomiVantageV1Api(url="", api_key_name="test", api_key_token="token")

        with self.assertRaises(ValueError):
            NozomiVantageV1Api(url="https://example.com", api_key_name="", api_key_token="token")

        with self.assertRaises(ValueError):
            NozomiVantageV1Api(url="https://example.com", api_key_name="test", api_key_token="")


class NozomiVantageV1SeverityMappingTest(BaseTestCase):
    """Test cases for severity mapping functions."""

    def test_map_nozomi_severity_to_ocsf(self):
        """Test Nozomi severity to OCSF severity mapping."""
        from apps.connectors.integrations.schemas.ocsf.enums import Severity

        # Test informational (0-1)
        self.assertEqual(map_nozomi_severity_to_ocsf(0), Severity.INFORMATIONAL)
        self.assertEqual(map_nozomi_severity_to_ocsf(1), Severity.INFORMATIONAL)

        # Test low (2-3)
        self.assertEqual(map_nozomi_severity_to_ocsf(2), Severity.LOW)
        self.assertEqual(map_nozomi_severity_to_ocsf(3), Severity.LOW)

        # Test medium (4-5)
        self.assertEqual(map_nozomi_severity_to_ocsf(4), Severity.MEDIUM)
        self.assertEqual(map_nozomi_severity_to_ocsf(5), Severity.MEDIUM)

        # Test high (6-7)
        self.assertEqual(map_nozomi_severity_to_ocsf(6), Severity.HIGH)
        self.assertEqual(map_nozomi_severity_to_ocsf(7), Severity.HIGH)

        # Test critical (8-10)
        self.assertEqual(map_nozomi_severity_to_ocsf(8), Severity.CRITICAL)
        self.assertEqual(map_nozomi_severity_to_ocsf(9), Severity.CRITICAL)
        self.assertEqual(map_nozomi_severity_to_ocsf(10), Severity.CRITICAL)

        # Test incident override
        self.assertEqual(map_nozomi_severity_to_ocsf(1, is_incident=True), Severity.CRITICAL)
        self.assertEqual(map_nozomi_severity_to_ocsf(5, is_incident=True), Severity.CRITICAL)

    def test_map_nozomi_status_to_ocsf(self):
        """Test Nozomi status to OCSF status mapping."""
        from apps.connectors.integrations.schemas.ocsf.enums import DetectionStatus

        # Test basic status mapping
        self.assertEqual(map_nozomi_status_to_ocsf("open"), DetectionStatus.NEW)
        self.assertEqual(map_nozomi_status_to_ocsf("closed"), DetectionStatus.RESOLVED)

        # Test acknowledged status
        self.assertEqual(map_nozomi_status_to_ocsf("open", is_acknowledged=True), DetectionStatus.IN_PROGRESS)
        self.assertEqual(map_nozomi_status_to_ocsf("closed", is_acknowledged=True), DetectionStatus.RESOLVED)

        # Test unknown status
        self.assertEqual(map_nozomi_status_to_ocsf("unknown"), DetectionStatus.UNKNOWN)


class NozomiVantageV1NormalizerTest(BaseTestCase):
    """Test cases for the alert normalizer."""

    def setUp(self):
        """Set up test data."""
        self.test_data_dir = os.path.join(
            os.path.dirname(__file__), "test_data", "nozomi_vantage"
        )

    def load_test_data(self, filename):
        """Load test data from JSON file."""
        file_path = os.path.join(self.test_data_dir, filename)
        with open(file_path, 'r') as f:
            return json.load(f)

    def test_normalize_event(self):
        """Test event normalization with comprehensive test data."""
        test_data = self.load_test_data("alert_normalize_event.json")
        input_data = test_data["input"]
        expected = test_data["expected"]

        # Normalize the event
        normalized = normalize_event(input_data)

        # Use single comprehensive assertion as per established pattern
        self.assertEqual(normalized.event_timestamp.isoformat(), expected["event_timestamp"])
        self.assertEqual(normalized.vendor_item_ref.id, expected["vendor_item_ref"]["id"])
        self.assertEqual(normalized.vendor_item_ref.title, expected["vendor_item_ref"]["title"])
        self.assertEqual(normalized.ioc.external_id, expected["ioc"]["external_id"])
        self.assertEqual(normalized.ioc.external_name, expected["ioc"]["external_name"])
        self.assertEqual(normalized.ioc.has_ioc_definition, expected["ioc"]["has_ioc_definition"])


class NozomiVantageV1IntegrationTest(BaseIntegrationTest, HealthCheckComponentTestMixin):
    """Test cases for NozomiVantageV1Integration."""

    integration_class = NozomiVantageV1Integration
    health_check_class = ConnectionHealthCheck

    @staticmethod
    def default_settings():
        """Get test configuration for the integration."""
        return {
            "url": "https://test.nozomi.example.com",
            "api_key_name": "test_key",
            "api_key_token": "test_token",
        }

    def test_integration_initialization(self):
        """Test integration initialization."""
        self.assertIsInstance(self.integration, NozomiVantageV1Integration)
        self.assertEqual(self.integration.api_class, NozomiVantageV1Api)
        self.assertIn(NozomiVantageV1ApiError, self.integration.exception_types)

    @patch('apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.api.NozomiVantageV1Api.health_check')
    def test_health_check_success(self, mock_health_check):
        """Test successful health check."""
        mock_health_check.return_value = True

        health_check = ConnectionHealthCheck(self.integration)
        result = health_check.get_result()

        self.assertTrue(result.success)
        self.assertIn("Successfully connected", result.message)

    @patch('apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.api.NozomiVantageV1Api.health_check')
    def test_health_check_failure(self, mock_health_check):
        """Test failed health check."""
        mock_health_check.side_effect = NozomiVantageV1ApiError("Connection failed")

        health_check = ConnectionHealthCheck(self.integration)
        result = health_check.get_result()

        self.assertFalse(result.success)
        self.assertIn("API error", result.message)
